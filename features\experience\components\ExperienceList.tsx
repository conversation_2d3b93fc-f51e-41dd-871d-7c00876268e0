import React from "react";
import {
  FlatList,
  Text,
  View,
  RefreshControl,
  ActivityIndicator,
  ListRenderItem,
} from "react-native";
import { cn } from "@/lib/utils";
import { ExperienceType } from "@/features/experience/model";
import { useTranslation } from "react-i18next";

type ExperienceListProps = {
  experiences: ExperienceType[];
  isLoading?: boolean;
  isError?: boolean;
  isRefreshing?: boolean;
  onRefresh?: () => void;
  onLoadMore?: () => void;
  hasNextPage?: boolean;
  isLoadingMore?: boolean;
  className?: string;
  emptyStateTitle?: string;
  emptyStateDescription?: string;
  children: ListRenderItem<ExperienceType>;
};

const ExperienceList = ({
  experiences,
  isLoading = false,
  isError = false,
  isRefreshing = false,
  onRefresh,
  onLoadMore,
  hasNextPage = false,
  isLoadingMore = false,
  className,
  emptyStateTitle,
  emptyStateDescription,
  children: renderItem,
}: ExperienceListProps) => {
  const { t } = useTranslation();

  const renderEmptyState = () => {
    if (isLoading) {
      return (
        <View className="flex-1 justify-center items-center py-lg">
          <ActivityIndicator size="large" className="text-primary-1" />
          <Text className="text-dark-secondary text-xl mt-5">
            {t("common.loading")}
          </Text>
        </View>
      );
    }

    // Handle error state
    if (isError) {
      return (
        <View className="flex-1 justify-center items-center py-lg px-xs">
          <Text className={cn("text-4xl text-dark-primary text-center mb-sm")}>
            {t("experience.errors.load_experiences_title")}
          </Text>
          <Text className={cn("text-xl text-light-secondary text-center")}>
            {t("experience.errors.load_experiences_message")}
          </Text>
        </View>
      );
    }

    // Handle empty state
    return (
      <View className="flex-1 justify-center items-center py-lg px-xs">
        <Text className={cn("text-4xl text-dark-primary text-center mb-sm")}>
          {emptyStateTitle || t("experience.no_experiences_title")}
        </Text>
        <Text className={cn("text-xl text-light-secondary text-center")}>
          {emptyStateDescription || t("experience.no_experiences_description")}
        </Text>
      </View>
    );
  };

  const renderFooter = () => {
    if (!isLoadingMore || !hasNextPage) return null;

    return (
      <View className="py-xs justify-center items-center">
        <ActivityIndicator size="small" className="text-primary-1" />
        <Text className="text-light-secondary text-sm mt-1">
          {t("experience.loading_more")}
        </Text>
      </View>
    );
  };

  const handleEndReached = () => {
    if (hasNextPage && !isLoadingMore && !isLoading) {
      onLoadMore?.();
    }
  };

  return (
    <FlatList
      data={experiences}
      renderItem={renderItem}
      keyExtractor={(item) => item._id}
      className={cn("flex-1", className)}
      contentContainerClassName="px-5 gap-5 pt-2.5"
      showsVerticalScrollIndicator={true}
      refreshControl={
        onRefresh ? (
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={onRefresh}
            colors={["#BE0068"]}
            tintColor="#BE0068"
          />
        ) : undefined
      }
      onEndReached={handleEndReached}
      onEndReachedThreshold={0.3}
      ListEmptyComponent={renderEmptyState}
      ListFooterComponent={renderFooter}
      removeClippedSubviews={true}
      maxToRenderPerBatch={10}
      windowSize={10}
    />
  );
};

export default ExperienceList;
