import InfoItem, { InfoItemText } from "@/features/shared/components/InfoItem";
import { ExperienceType } from "@/features/experience/model";
import { inKwanza } from "@/features/shared/utils";
import { cn } from "@/lib/utils";
import {
  <PERSON><PERSON>,
  FontAwesome6,
  Ionicons,
  MaterialCommunityIcons,
} from "@expo/vector-icons";
import * as Linking from "expo-linking";
import React from "react";
import { useTranslation } from "react-i18next";
import { Text, TouchableOpacity, View } from "react-native";

const ExperienceDetailInfo = ({
  experience,
}: {
  experience: ExperienceType;
}) => {
  const { t } = useTranslation();

  const openMaps = () => {
    const mapLink = experience.locations?.[0]?.mapLink;
    if (mapLink) {
      Linking.openURL(mapLink);
    }
  };

  const getLocationText = () => {
    const location = experience.locations?.[0];
    if (!location) return "Location not available";
    return `${location.name}, ${location.province}`;
  };

  return (
    <View className="flex-col gap-8">
      {/* Highlights (Destaques) */}
      {experience.features && experience.features.length > 0 && (
        <View className="flex-col gap-2.5">
          <View className="flex-row items-center gap-2">
            <Ionicons
              name="sparkles-outline"
              className="text-dark-secondary text-2xl"
            />
            <Text className="text-xl font-semibold text-dark-secondary">
              {t("experience.highlights")}
            </Text>
          </View>
          <View className="flex-row flex-wrap gap-x-4 gap-y-1">
            {experience.features.map((feature, index) => (
              <InfoItem
                key={index}
                title={feature.name}
                description={feature.description}
              >
                <>
                  <Ionicons
                    name="checkmark"
                    className="text-dark-secondary text-lg"
                  />
                  <InfoItemText
                    title={feature.name}
                    description={feature.description}
                  />
                </>
              </InfoItem>
            ))}
          </View>
        </View>
      )}
      {/* Description */}
      <View className="flex-col gap-2.5">
        <View className="flex-row items-center gap-2">
          <Feather name="align-left" className="text-dark-secondary text-2xl" />
          <Text className="text-xl font-semibold text-dark-secondary">
            {t("experience.description")}
          </Text>
        </View>
        <Text className="text-lg text-dark-secondary leading-6">
          {experience.description}
        </Text>
      </View>

      {/* Price */}
      {experience.prices && experience.prices.length > 0 && (
        <View className="flex-col gap-2.5">
          <View className="flex-row items-center gap-2">
            <FontAwesome6
              name="money-bill-1"
              className="text-light-secondary text-xl"
            />
            <Text className="text-lg font-semibold text-dark-secondary">
              {t("experience.prices")}
            </Text>
          </View>
          <View className="flex-row flex-wrap gap-x-4 gap-y-1">
            {experience.prices.map((price, index) => (
              <InfoItem
                key={index}
                title={inKwanza(price.price)}
                description={price.description}
              >
                <>
                  {price.type === "RELATIVE" && (
                    <Text
                      className={cn(
                        "text-base text-dark-secondary flex-shrink"
                      )}
                    >
                      A partir de
                    </Text>
                  )}
                  <InfoItemText
                    title={inKwanza(price.price)}
                    description={price.description}
                  />
                </>
              </InfoItem>
            ))}
          </View>
        </View>
      )}

      {/* Location */}
      <View className="flex-col gap-2.5">
        <View className="flex-row items-center gap-2">
          <Ionicons
            name="location-outline"
            className="text-dark-secondary text-3xl"
          />
          <Text className="text-lg font-semibold text-dark-secondary">
            {t("experience.location")}
          </Text>
        </View>
        <TouchableOpacity
          disabled={!experience.locations?.[0]?.mapLink}
          onPress={openMaps}
        >
          <Text
            className={cn(
              "text-lg text-tertiary-2",
              experience.locations?.[0]?.mapLink && "text-primary-1 font-medium"
            )}
          >
            {getLocationText()}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Availability */}
      {experience.availability && experience.availability.length > 0 && (
        <View className="flex-col gap-2.5">
          <View className="flex-row items-center gap-2">
            <MaterialCommunityIcons
              name="calendar-clock-outline"
              className="text-dark-secondary text-2xl"
            />
            <Text className="text-lg font-semibold text-dark-secondary">
              {t("experience.availability")}
            </Text>
          </View>
          <View className="flex-row flex-wrap gap-x-4 gap-y-1">
            {experience.availability.map((availability, index) => (
              <Text key={index} className="text-lg text-dark-secondary">
                {availability.startAt} - {availability.endAt} (
                {availability.label})
              </Text>
            ))}
          </View>
        </View>
      )}

      {/* Details */}
      {experience.details && experience.details.length > 0 && (
        <View className="flex-col gap-2.5">
          <View className="flex-row items-center gap-2">
            <Ionicons name="list" className="text-dark-secondary text-2xl" />
            <Text className="text-lg font-semibold text-dark-secondary">
              {t("experience.details")}
            </Text>
          </View>
          <View className="flex-row flex-wrap gap-x-4 gap-y-1">
            {experience.details.map((detail, index) => (
              <InfoItem
                key={index}
                title={detail.title}
                description={detail.description}
              >
                <InfoItemText
                  title={detail.title}
                  description={detail.description}
                />
              </InfoItem>
            ))}
          </View>
        </View>
      )}
    </View>
  );
};

export default ExperienceDetailInfo;
