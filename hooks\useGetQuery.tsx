import { ApiUrlValuesType, fetchApi } from "../config/api";
import { mergePathnameQuery } from "@/config/query-utils";
import { createHandleErrorDialog } from "@/lib/errors";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import i18n from "@/lib/i18n/config";

type Props<T> = {
  queryKey: string;
  apiUrl: ApiUrlValuesType;
  initialData: T;
  enabled?: boolean;
  query?: Record<string, string | number | boolean | object> | undefined;
  showErrors?: boolean;
  gcTime?: number;
};

const useGetQuery = <ResponseType,>({
  queryKey,
  apiUrl,
  initialData,
  query,
  enabled = true,
  showErrors = false,
  gcTime,
}: Props<ResponseType>) => {
  const qClient = useQueryClient();

  const { data, isLoading, isFetching, refetch, isError } = useQuery({
    queryKey: query ? [queryKey, query] : [queryKey],
    queryFn: async () => {
      const url = mergePathnameQuery(apiUrl, query);
      const response = await fetchApi<ResponseType>(url);
      return response.data;
    },
    enabled,
    initialData,
    throwOnError: (err: any) => {
      if (showErrors) {
        createHandleErrorDialog({
          title: i18n.t("experience.errors.general_error"),
          error: err,
        });
      }
      return false;
    },
    gcTime,
  });

  const handleRefresh = async (externalKey?: string) => {
    await qClient.invalidateQueries({
      queryKey: externalKey ? [externalKey] : [queryKey],
    });
    await refetch();
  };

  return {
    data: data || initialData,
    isLoading,
    isFetching,
    isError,
    refetch,
    handleRefresh,
  };
};

export default useGetQuery;
