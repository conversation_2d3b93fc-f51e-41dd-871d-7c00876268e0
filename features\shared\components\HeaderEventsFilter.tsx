import React, { useRef, useEffect } from "react";
import { Pressable, TextInput, View } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import InputField from "./InputField";
import useEventFilterStore from "@/features/events/useEventFilterStore";
import globalStyles from "@/lib/globalStyles";
import { router, usePathname } from "expo-router";
import { useDebounce } from "@/hooks/useDebounce";
import { useTranslation } from "react-i18next";

const HeaderEventsFilter = () => {
  const { search, setShowFilters, showFilters, setSearch } =
    useEventFilterStore();

  const pathname = usePathname();
  const inputSearchRef = useRef<TextInput>(null);
  const isSearchScreen = pathname === "/search";
  const { t } = useTranslation();

  const debouncedHandleSearch = useDebounce((text: string) => {
    setSearch(text);

    if (text.length > 0 && !isSearchScreen) {
      router.push("/search");

      setTimeout(() => {
        inputSearchRef.current?.focus();
      }, 500);
    }
  }, 1000);

  useEffect(() => {
    if (!search && inputSearchRef.current) {
      inputSearchRef.current.clear();
    }
  }, [search]);

  return (
    <View
      style={{
        flexDirection: "row",
        alignItems: "center",
        gap: globalStyles.gap["2xs"],
      }}
    >
      <InputField
        placeholder={t("search.title")}
        onChangeText={debouncedHandleSearch}
        defaultValue={search}
        ref={inputSearchRef}
        style={{ borderRadius: globalStyles.rounded.full }}
      />
      {isSearchScreen && !showFilters && (
        <Pressable onPress={() => setShowFilters(true)}>
          <Ionicons
            name="filter"
            size={24}
            color={globalStyles.colors.primary1}
          />
        </Pressable>
      )}
    </View>
  );
};

export default HeaderEventsFilter;
