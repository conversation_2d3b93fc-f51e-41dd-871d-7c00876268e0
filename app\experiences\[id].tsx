import React, { useCallback, useState } from "react";
import { Al<PERSON>, Share, Text, View } from "react-native";
import Button from "@/features/shared/components/Button";
import Layout from "@/features/shared/components/Layout";
import ExperienceDetailHeader from "@/features/experience/components/ExperienceDetailHeader";
import ExperienceDetailImage from "@/features/experience/components/ExperienceDetailImage";
import ExperienceDetailInfo from "@/features/experience/components/ExperienceDetailInfo";
import Loading from "@/features/shared/components/Loading";
import { useLocalSearchParams, router, useFocusEffect } from "expo-router";
import { SITE_URL } from "@/config/api";
import { useTranslation } from "react-i18next";
import { useGetExperienceByIdQuery } from "@/features/experience/hooks";
import { createHandleErrorDialog } from "@/lib/errors";
import CheckoutModal from "@/features/shared/components/CheckoutModal";

const ExperienceDetailsScreen = () => {
  const { t } = useTranslation();
  const { id } = useLocalSearchParams();

  if (!id || typeof id !== "string") {
    router.replace("/");
    return createHandleErrorDialog({
      title: t("experience.errors.loading_title"),
      error: new Error(t("experience.errors.loading_message")),
    });
  }

  const {
    data: experience,
    isLoading,
    refetch,
  } = useGetExperienceByIdQuery({ id });

  useFocusEffect(
    useCallback(() => {
      refetch();
    }, [refetch, id])
  );

  const [isItemsModalOpen, setIsItemsModalOpen] = useState(false);
  const [isCheckoutModalOpen, setIsCheckoutModalOpen] = useState(false);

  if (isLoading || !experience) {
    return <Loading />;
  }

  if (!experience && !isLoading) {
    return (
      <Layout headless={false}>
        <View className="flex-1 h-[80vh] justify-center items-center">
          <Text className="text-dark-primary text-2xl">
            {t("experience.errors.not_found")}
          </Text>
        </View>
      </Layout>
    );
  }

  const onShare = async () => {
    try {
      const result = await Share.share({
        message: `${SITE_URL}/experiences/${experience._id}`,
      });

      if (result.action === Share.sharedAction) {
        if (result.activityType) {
          // shared with activity type of result.activityType
        } else {
          // shared
        }
      } else if (result.action === Share.dismissedAction) {
        // dismissed
      }
    } catch (error: any) {
      Alert.alert(error.message);
    }
  };

  const onViewItems = () => {
    setIsItemsModalOpen(true);
  };

  const hasItems = !!(experience.items && experience.items.length > 0);

  return (
    <>
      <Layout headless={false}>
        <View className="gap-8 flex-col">
          <ExperienceDetailHeader experience={experience} />

          <ExperienceDetailImage
            experience={experience}
            onShare={onShare}
            onViewItems={onViewItems}
            hasItems={hasItems}
          />

          <ExperienceDetailInfo experience={experience} />
          <View className="h-20" />
        </View>
      </Layout>

      {experience.checkoutMethods && experience.checkoutMethods.length > 0 && (
        <>
          <Button
            text={t("experience.contact")}
            style={{
              position: "absolute",
              width: "95%",
              bottom: 30,
              alignSelf: "center",
            }}
            onPress={() => setIsCheckoutModalOpen(true)}
          />
          <CheckoutModal
            isOpen={isCheckoutModalOpen}
            onClose={() => setIsCheckoutModalOpen(false)}
            data={{ name: experience.name }}
            checkouts={experience.checkoutMethods}
          />
        </>
      )}
    </>
  );
};

export default ExperienceDetailsScreen;
