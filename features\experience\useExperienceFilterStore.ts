import { ANGOLA_PROVINCES, AngolaProvinceKey } from "@/lib/angola-provinces";
import { create } from "zustand";

export type ExperienceSortItem = {
  key: "name_asc" | "name_desc" | "higher_price" | "lower_price" | "featured";
  by: "name" | "createdAt" | "updatedAt" | "type" | "price";
  order: "asc" | "desc";
};

export const experienceSortItems: ExperienceSortItem[] = [
  {
    key: "name_asc",
    by: "name",
    order: "asc",
  },
  {
    key: "name_desc",
    by: "name",
    order: "desc",
  },
  {
    key: "higher_price",
    by: "price",
    order: "desc",
  },
  {
    key: "lower_price",
    by: "price",
    order: "asc",
  },
  {
    key: "featured",
    by: "createdAt",
    order: "desc",
  },
];

export type ExperienceFilterType = {
  key: "name" | "location" | "province" | "sort";
  value: string | undefined;
  type: "text" | "select";
  labelKey:
    | "common.name"
    | "common.location"
    | "common.province"
    | "common.sort_by";
  valueKey?: `common.${ExperienceSortItem["key"]}`;
};

export type ExperienceFilterFields = {
  [K in ExperienceFilterType["key"]]?: ExperienceFilterType["value"];
};

export const searchFieldOptions: ExperienceFilterType[] = [
  {
    key: "name",
    labelKey: "common.name",
    value: undefined,
    type: "text",
  },
  {
    key: "location",
    labelKey: "common.location",
    value: undefined,
    type: "text",
  },
  {
    key: "province",
    labelKey: "common.province",
    value: undefined,
    type: "select",
  },
];

export type ExperienceFilterStore = {
  searchField: ExperienceFilterType;
  filterFields: ExperienceFilterType[];
  sort?: ExperienceSortItem;
  showFilters: boolean;
  addOrUpdateFilter: (filter: ExperienceFilterType) => void;
  setSearchField: (field: ExperienceFilterType) => void;
  setSort: (sort?: ExperienceSortItem) => void;
  clearFilter: (type?: ExperienceFilterType["key"]) => void;
  clearAllFilters: () => void;
  setShowFilters: (show: boolean) => void;
  getFilterResultList: (params: {
    key: ExperienceFilterType["key"];
    text?: string;
  }) => Array<{ key: string; value: string }> | undefined;
};

const useExperienceFilterStore = create<ExperienceFilterStore>((set, get) => ({
  searchField: {
    key: "name",
    labelKey: "common.name",
    value: undefined,
    type: "text",
  },
  filterFields: [],
  showFilters: true,
  sort: undefined,
  addOrUpdateFilter: (filter) => {
    set((prev) => {
      const filterExists = prev.filterFields.some((v) => v.key === filter.key);
      if (!filterExists) {
        return {
          filterFields: [filter, ...prev.filterFields],
        };
      }
      return {
        filterFields: prev.filterFields.map((v) =>
          v.key === filter.key ? filter : v
        ),
      };
    });
  },

  setShowFilters: (show) => set({ showFilters: show }),
  clearAllFilters: () => set({ filterFields: [], sort: undefined }),
  setSort: (sort) => set({ sort }),
  setSearchField: (field) => set({ searchField: field }),
  clearFilter: (type) =>
    set((prev) => ({
      filterFields: prev.filterFields.filter((v) => v.key !== type),
    })),
  getFilterResultList: ({ key, text }) => {
    const resultLists = {
      province: Object.entries(ANGOLA_PROVINCES).map(([key, value]) => ({
        key,
        value,
      })),
      sort: undefined,
      name: undefined,
      location: undefined,
    };

    if (text) {
      resultLists.province = resultLists.province.filter((v) =>
        v.value.toLowerCase().includes(text.toLowerCase())
      );
    }

    return resultLists?.[key];
  },
}));

export default useExperienceFilterStore;
