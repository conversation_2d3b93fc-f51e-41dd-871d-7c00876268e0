import Button from "@/features/shared/components/Button";
import InputField from "@/features/shared/components/InputField";
import InputSelect from "@/features/shared/components/InputSelect";
import MyDatePicker from "@/features/shared/components/MyDatePicker";
import AuthHeader from "@/features/auth/components/AuthHeader";
import useAppAuth, {
  myUserProfileSchema,
} from "@/features/auth/hooks/useAppAuth";
import { createHandleErrorDialog } from "@/lib/errors";
import { createLogoutAlert } from "@/features/auth/logoutAlert";
import globalStyles from "@/lib/globalStyles";
import { LinearGradient } from "expo-linear-gradient";
import { router, useFocusEffect } from "expo-router";
import React, {
  useCallback,
  useEffect,
  useMemo,
  useState,
  useTransition,
} from "react";
import { ScrollView, View, BackHandler } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { useTranslation } from "react-i18next";
import { TFunction } from "i18next";
import { z } from "zod";

const editProfileSchema = (t: TFunction<"translation", undefined>) =>
  myUserProfileSchema(t).omit({
    id: true,
    email: true,
    emailVerified: true,
    name: true,
  });

type EditUserParams = z.infer<ReturnType<typeof editProfileSchema>>;

export default function EditProfileScreen() {
  const { user, userClient, isProfileComplete, signOut } = useAppAuth();
  const [isLoading, setIsLoading] = useState(false);
  const insets = useSafeAreaInsets();
  const { t } = useTranslation();

  const [isLogoutPending, startLogout] = useTransition();

  const isProfileCompletionFlow = !isProfileComplete();

  const genderOptions = useMemo(
    () => [
      { name: t("profile.gender_male"), value: "male" },
      { name: t("profile.gender_female"), value: "female" },
      { name: t("profile.gender_prefer_not_say"), value: "prefer_not_say" },
    ],
    [t]
  );

  const [formFields, setFormFields] = useState<Partial<EditUserParams>>({
    firstName: user?.firstName,
    lastName: user?.lastName,
    phoneNumber: user?.phoneNumber,
    website: user?.website,
    gender: user?.gender || "prefer_not_say",
    birthday: user?.birthday,
  });

  useEffect(() => {
    if (!user) return;

    setFormFields({
      firstName: user?.firstName,
      lastName: user?.lastName,
      phoneNumber: user?.phoneNumber,
      website: user?.website,
      gender: user?.gender || "prefer_not_say",
      birthday: user?.birthday,
    });
  }, [user]);

  useFocusEffect(
    useCallback(() => {
      if (isProfileCompletionFlow) {
        const onBackPress = () => {
          return true;
        };

        const subscription = BackHandler.addEventListener(
          "hardwareBackPress",
          onBackPress
        );
        return () => subscription?.remove();
      }
    }, [isProfileCompletionFlow])
  );

  const handleChange = (fieldName: keyof typeof formFields, value: string) => {
    setFormFields({
      ...formFields,
      [fieldName]: value,
    });
  };

  const handleDateChange = (newDate: Date) => {
    setFormFields({
      ...formFields,
      birthday: newDate,
    });
  };

  const handleSubmit = useCallback(async () => {
    try {
      if (!userClient || !user) return;
      setIsLoading(true);
      const prefixProtocol = (url?: string) =>
        !url
          ? url
          : ["http://", "https://"].some((prefix) => url.startsWith(prefix))
          ? url
          : `https://${url}`;

      formFields.website = prefixProtocol(formFields.website) || undefined;

      const validatedData = editProfileSchema(t).parse(formFields);

      await userClient.update({
        firstName: validatedData.firstName,
        lastName: validatedData.lastName,
        unsafeMetadata: {
          phoneNumber: validatedData.phoneNumber,
          website: validatedData.website,
          gender: validatedData.gender,
          birthday: validatedData.birthday?.toISOString(),
        },
      });

      if (isProfileCompletionFlow) {
        router.replace("/(tabs)");
      } else {
        router.back();
      }
    } catch (err: any) {
      createHandleErrorDialog({
        title: t("profile.update_profile_error"),
        message: t("profile.update_profile_error_message"),
        error: err,
      });
    } finally {
      setIsLoading(false);
    }
  }, [userClient, user, formFields, t]);

  return (
    <>
      <LinearGradient
        colors={["#E9E8ED", "#ceccd7"]}
        locations={[0.5, 0.9]}
        style={{
          width: "100%",
          height: "100%",
          position: "absolute",
          flex: 1,
          flexDirection: "column",
          zIndex: -1,
        }}
      />
      <View style={{ flex: 1, paddingTop: insets.top, overflow: "hidden" }}>
        <ScrollView
          style={{ flex: 1 }}
          contentContainerStyle={{ paddingBottom: insets.bottom }}
          keyboardShouldPersistTaps="handled"
        >
          <View
            style={{
              flex: 1,
              paddingTop: globalStyles.size.pageTop,
              paddingBottom: globalStyles.gap.xs,
              paddingHorizontal: globalStyles.gap.xs,
              gap: globalStyles.gap.md,
              justifyContent: "flex-start",
            }}
          >
            <AuthHeader
              description={
                isProfileCompletionFlow
                  ? t("profile.complete_profile_description")
                  : t("profile.edit_profile")
              }
            />
            <View
              style={{
                flexDirection: "column",
                gap: globalStyles.gap.sm,
              }}
            >
              <InputField
                required
                theme="secondary"
                placeholder={t("common.name")}
                defaultValue={formFields?.firstName}
                value={formFields.firstName}
                onChangeText={(txt) => handleChange("firstName", txt)}
              />
              <InputField
                required
                theme="secondary"
                placeholder={t("common.last_name")}
                defaultValue={formFields?.lastName}
                value={formFields.lastName}
                onChangeText={(txt) => handleChange("lastName", txt)}
              />
              <InputField
                theme="secondary"
                placeholder={t("common.website")}
                defaultValue={formFields?.website}
                value={formFields.website}
                onChangeText={(txt) => handleChange("website", txt)}
              />
              <InputField
                required
                theme="secondary"
                placeholder={t("profile.phone_number")}
                defaultValue={formFields?.phoneNumber}
                value={formFields.phoneNumber}
                onChangeText={(txt) => handleChange("phoneNumber", txt)}
                keyboardType="phone-pad"
              />
              <InputSelect
                required
                label={t("profile.gender")}
                items={genderOptions}
                selected={genderOptions.find(
                  (v) => v.value === formFields.gender
                )}
                onSelectItem={(item) => handleChange("gender", item.value)}
                theme="secondary"
                style={{
                  borderRadius: globalStyles.rounded.xs,
                  borderColor: "transparent",
                }}
                textStyle={{
                  color: globalStyles.rgba().primary1,
                }}
                defaultValue={formFields?.gender}
              />
              <MyDatePicker
                required
                date={formFields.birthday}
                label={t("profile.birth_date")}
                onChange={handleDateChange}
                theme="secondary"
                style={{
                  backgroundColor: globalStyles.colors.white,
                  borderRadius: globalStyles.rounded.xs,
                }}
                defaultValue={formFields?.birthday}
              />
            </View>
            <View
              style={{
                gap: globalStyles.gap.xs,
                flexDirection: isProfileCompletionFlow ? "column" : "row",
              }}
            >
              <Button
                isLoading={isLoading}
                text={
                  isProfileCompletionFlow
                    ? t("profile.complete_profile")
                    : t("profile.save_changes")
                }
                onPress={handleSubmit}
                style={{ flex: 1 }}
              />
              {!isProfileCompletionFlow && (
                <Button
                  text={t("common.cancel")}
                  style={{
                    backgroundColor: globalStyles.rgba({ opacity: 0.6 }).light
                      .secondary,
                  }}
                  onPress={() => router.back()}
                  disabled={isLoading}
                />
              )}
              {isProfileCompletionFlow && (
                <Button
                  text={t("auth.sign_out")}
                  type="link"
                  onPress={() =>
                    createLogoutAlert(t, () => startLogout(signOut))
                  }
                  disabled={isLoading || isLogoutPending}
                  isLoading={isLogoutPending}
                  style={{ alignSelf: "center" }}
                  textStyle={{
                    color: globalStyles.rgba().secondary2,
                  }}
                />
              )}
            </View>
          </View>
        </ScrollView>
      </View>
    </>
  );
}
