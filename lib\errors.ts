import { Alert } from "react-native";
import z from "zod";
import * as <PERSON><PERSON> from "@sentry/react-native";
import i18n from "@/lib/i18n/config";

export const handleErrors = ({
  error,
  message,
}: {
  error: any;
  message?: string;
}): string => {
  !__DEV__ && Sentry.captureException(error);
  if (error instanceof z.ZodError || "issues" in error) {
    return z.prettifyError(error);
  }

  return message || i18n.t("experience.errors.general_error");
};

export const createHandleErrorDialog = ({
  title,
  btnText,
  message,
  error,
}: {
  error: Error;
  message?: string;
  title: string;
  btnText?: string;
}) => {
  return Alert.alert(title, handleErrors({ error, message }), [
    {
      text: btnText || "OK",
    },
  ]);
};
